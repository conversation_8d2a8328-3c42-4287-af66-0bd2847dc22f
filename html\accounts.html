<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Account Settings</title>
    <link rel="stylesheet" href="../css/style_accounts.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cinzel:wght@400;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="game-container">
        <a href="mainpage.html" class="x-button">
            <img src="../images/x.svg" alt="Close">
        </a>

        <header class="game-header">
            <h1>⚙️ Account Settings ⚙️</h1>
        </header>

        <main class="accounts-editor">
            <section class="form-section">
                <form class="accounts-form" id="accounts-form" method="POST">
                    <div class="form-group">
                        <label for="username">Username</label>
                        <div class="input-with-badge">
                            <input type="text" id="username" readonly>
                            <span class="input-badge complete">🔒</span>
                        </div>
                        <small class="field-note">Username cannot be changed</small>
                    </div>

                    <div class="form-group">
                        <label for="email">Email Address</label>
                        <div class="input-with-badge">
                            <input type="email" id="email">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="password">Password</label>
                        <div class="password-wrapper">
                            <input type="password" id="password" value="************" readonly>
                            <button type="button" id="show-password-btn" class="btn-show">Show</button>
                        </div>
                        <a href="#" class="forgot-password">Forgot Password?</a>
                    </div>

                    <div class="form-actions">
                        <button type="submit" class="btn-save">💾 Save Changes</button>
                        <button type="button" class="btn-cancel" onclick="window.location.href='mainpage.html'">🚫 Cancel</button>
                    </div>
                </form>
            </section>
        </main>
    </div>

    <!-- Password Modal -->
    <div id="password-modal" class="modal">
        <div class="modal-content">
            <span class="close-btn">&times;</span>
            <h2>Enter Your Password</h2>
            <p>To view or change your password, please enter your current password.</p>
            <input type="password" id="modal-password-input" placeholder="Current Password">
            <button id="submit-modal-password" class="btn-submit">Submit</button>
            <p id="modal-error" class="error-message"></p>
        </div>
    </div>

    <script src="../js/accounts.js"></script>
</body>
</html>
