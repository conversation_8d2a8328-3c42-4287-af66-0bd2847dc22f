<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Who Wants to Be a Millionaire - Robot Edition</title>
    <link rel="stylesheet" href="../../css/style_millionaire.css">
</head>
<body data-level="3">
    <div class="game-container">
        <!-- Instruction Modal -->
        <div class="modal instructions-modal" id="instruction-modal" role="dialog" aria-labelledby="instruction-title" style="display: none;">
            <div class="modal-content">
                <h2 id="instruction-title">Who Wants to Be a Millionaire?</h2>
                <div class="instructions">
                    <p>Welcome to the ultimate quiz challenge!</p>
                    <ul>
                        <li>Answer questions correctly to win</li>
                        <li>Use lifelines: 50:50, Ask Audience, Phone Friend</li>
                        <li>You can walk away with your current winnings</li>
                        <li>Lose all your lives and you lose everything</li>
                    </ul>
                </div>
                <button class="start-btn" id="start-btn" aria-label="Start the game">Let's Play!</button>
            </div>
        </div>

        <!-- Game Area -->
        <div class="game-area" id="game-area">
            <!-- Header with Game Info -->
            <div class="game-header">
                <div class="game-info">
                    <div class="timer">⏱️ <span id="time">30</span>s</div>
                    <div class="current-prize">Current: $<span id="current-prize">0</span></div>
                </div>
            </div>

            <!-- Horizontal Prize Ladder -->
            <div class="prize-ladder-horizontal">
                <h3>Prize Ladder</h3>
                <div class="ladder-scroll-container">
                    <div class="ladder-horizontal" id="ladder-horizontal">
                        <!-- Prize items will be generated by JS -->
                    </div>
                </div>
            </div>

            <!-- Question Area -->
            <div class="question-area">
                <div class="question-header">
                    <span class="question-number">Question <span id="question-num">1</span> </span>
                    <span class="question-value">for $<span id="question-value">100</span></span>
                </div>

                <!-- Host and Question Container -->
                <div class="host-question-container">
                    <!-- Pikachu Host -->
                    <div class="quiz-host" id="quiz-host">
                        <div class="pikachu-character">
                            <div class="pikachu-body">
                                <div class="pikachu-face">
                                    <div class="pikachu-eyes">
                                        <div class="eye left-eye"></div>
                                        <div class="eye right-eye"></div>
                                    </div>
                                    <div class="pikachu-cheeks">
                                        <div class="cheek left-cheek"></div>
                                        <div class="cheek right-cheek"></div>
                                    </div>
                                    <div class="pikachu-mouth"></div>
                                </div>
                                <div class="pikachu-ears">
                                    <div class="ear left-ear"></div>
                                    <div class="ear right-ear"></div>
                                </div>
                            </div>
                            <div class="pikachu-arms">
                                <div class="arm left-arm"></div>
                                <div class="arm right-arm pointing"></div>
                            </div>
                        </div>
                        <div class="host-speech-bubble">
                            <div class="speech-bubble-content">
                                <span class="host-text">Here's your question!</span>
                            </div>
                        </div>
                    </div>

                    <!-- Question Text -->
                    <div class="question-text" id="question-text">
                        Question will appear here...
                    </div>
                </div>
                <div class="options-container" id="options-container">
                    <div class="option" data-option="A">
                        <span class="option-letter">A:</span>
                        <span class="option-text">Option A</span>
                    </div>
                    <div class="option" data-option="B">
                        <span class="option-letter">B:</span>
                        <span class="option-text">Option B</span>
                    </div>
                    <div class="option" data-option="C">
                        <span class="option-letter">C:</span>
                        <span class="option-text">Option C</span>
                    </div>
                    <div class="option" data-option="D">
                        <span class="option-letter">D:</span>
                        <span class="option-text">Option D</span>
                    </div>
                </div>
            </div>

            <!-- Lifelines and Controls -->
            <div class="game-controls">
                <div class="lifelines">
                    <button class="lifeline" id="fifty-fifty" title="50:50 - Remove two wrong answers">
                        <span class="lifeline-icon">🎯</span>
                        <span class="lifeline-text">50:50</span>
                    </button>
                    <button class="lifeline" id="ask-audience" title="Ask the Audience">
                        <span class="lifeline-icon">👥</span>
                        <span class="lifeline-text">Audience</span>
                    </button>
                    <button class="lifeline" id="phone-friend" title="Phone a Friend">
                        <span class="lifeline-icon">📞</span>
                        <span class="lifeline-text">Friend</span>
                    </button>
                </div>
                <div class="action-buttons">
                    <button id="walk-away-btn" class="walk-away">Walk Away</button>
                    <button id="final-answer-btn" class="final-answer" disabled>Final Answer</button>
                </div>
            </div>
        </div>

        <!-- Results Modal -->
        <div class="modal results-modal" id="results-modal" role="dialog" aria-labelledby="result-title" style="display: none;">
            <div class="modal-content">
                <h2 id="result-title" class="result-title">Game Over!</h2>
                <div id="result-stars" class="stars" aria-label="Star rating"></div>
                <p id="result-message" class="result-message">You won $0!</p>
                <div class="exp-container">
                    <span class="exp-icon">✨</span>
                    <span class="exp-text">+0 EXP</span>
                </div>
                <div class="modal-buttons">
                    <button class="replay-btn" id="play-again-btn">PLAY AGAIN</button>
                    <button class="main-menu-btn" id="back-to-menu-btn">MAIN MENU</button>
                </div>
            </div>
        </div>

        <!-- Lifeline Modals -->
        <div class="modal" id="audience-modal" style="display: none;">
            <div class="modal-content">
                <h3>Ask the Audience</h3>
                <div class="audience-results" id="audience-results">
                    <div class="audience-bar" data-option="A">
                        <span class="option-label">A:</span>
                        <div class="bar-container">
                            <div class="bar" style="width: 0%"></div>
                            <span class="percentage">0%</span>
                        </div>
                    </div>
                    <div class="audience-bar" data-option="B">
                        <span class="option-label">B:</span>
                        <div class="bar-container">
                            <div class="bar" style="width: 0%"></div>
                            <span class="percentage">0%</span>
                        </div>
                    </div>
                    <div class="audience-bar" data-option="C">
                        <span class="option-label">C:</span>
                        <div class="bar-container">
                            <div class="bar" style="width: 0%"></div>
                            <span class="percentage">0%</span>
                        </div>
                    </div>
                    <div class="audience-bar" data-option="D">
                        <span class="option-label">D:</span>
                        <div class="bar-container">
                            <div class="bar" style="width: 0%"></div>
                            <span class="percentage">0%</span>
                        </div>
                    </div>
                </div>
                <button id="close-audience-modal">Close</button>
            </div>
        </div>

        <div class="modal" id="friend-modal" style="display: none;">
            <div class="modal-content friend-modal-content">
                <div class="phone-call-header">
                    <div class="phone-icon">📞</div>
                    <h3>Phone a Friend</h3>
                    <div class="call-timer" id="call-timer" style="display: none;">
                        <span class="timer-text">Time: </span>
                        <span class="timer-value" id="timer-value">30</span>s
                    </div>
                </div>

                <div class="friend-profile" id="friend-profile">
                    <div class="friend-avatar" id="friend-avatar">👤</div>
                    <div class="friend-info">
                        <div class="friend-name" id="friend-name">Your Friend</div>
                        <div class="friend-expertise" id="friend-expertise">General Knowledge</div>
                    </div>
                    <div class="call-status" id="call-status">
                        <div class="status-indicator connecting" id="status-indicator"></div>
                        <span class="status-text" id="status-text">Connecting...</span>
                    </div>
                </div>

                <div class="conversation-area">
                    <div class="friend-advice">
                        <div class="typing-indicator" id="typing-indicator" style="display: none;">
                            <span></span>
                            <span></span>
                            <span></span>
                        </div>
                        <p id="friend-message">Your friend is thinking...</p>
                    </div>

                    <div class="interaction-buttons" id="interaction-buttons" style="display: none;">
                        <button class="interaction-btn" id="ask-clarification">Ask for clarification</button>
                        <button class="interaction-btn" id="ask-confidence">How confident are you?</button>
                    </div>
                </div>

                <div class="modal-footer">
                    <button id="close-friend-modal" class="close-call-btn">End Call</button>
                </div>
            </div>
        </div>
    </div>

    <script src="../../js/millionaire-quiz.js"></script>
</body>
</html>
